/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2022 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2024-03-11    毛玉泽，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/**
 * @file： atomic.S
 * @brief：
 *	    <li>原子操作代码。</li>
 */

/************************头 文 件******************************/
#define ASM_USE
#include <asm.h>
#include <cp15.h>

/************************宏 定 义******************************/

/************************类型定义******************************/

/************************外部声明******************************/

/************************前向声明******************************/

/************************模块变量******************************/

/************************函数实现******************************/

.text

/*
* @brief 
*    将target指向的值增加value，并返回target原有指向的值。
* @param[out]||[in]: target: 需要增加值的地址。
* @param[in]: value: 需要增加的值
* @retval target原有指向的值
*/
/* T_UWORD atomic32_add(T_UWORD * target, T_UWORD value) */
ENTRY(atomic32_add)
1:
	ldrex  r2, [r0]	            /* 加载旧值到r2,并标记 独占访问 */
	add	   r3, r2, r1	        /* 加上 value，结果存到r3 */
	strex  r12, r3, [r0]	    /* 尝试存储r3 */
	teq	   r12, #0		        /* 判断是否存储成功 */
	
	bne	   1b	                /* 未存储成功，则跳转重试 */

	mov	   r0, r2		        /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_add)	

/*
* @brief 
*	 将target指向的值增加value，并返回target增加后的值。
* @param[out]||[in]: target: 需要增加值的地址。
* @param[in]: value: 需要增加的值
* @retval target增加后的值
*/
/* T_UWORD atomic32_add_return(T_UWORD * target, T_UWORD value) */
ENTRY(atomic32_add_return)
1:
	ldrex  r2, [r0] 			/* 加载旧值到r2,并标记 独占访问 */
	add    r3, r2, r1			/* 加上 value，结果存到r3 */
	strex  r12, r3, [r0]		/* 尝试存储r3 */
	teq    r12, #0				/* 判断是否存储成功 */
	
	bne    1b					/* 未存储成功，则跳转重试 */

	mov    r0, r3				/* 返回新值 */
	mov    pc, lr
ENDPROC(atomic32_add_return)

/*
* @brief 
*       将target指向的值与value位或，并返回target原有的值。
* @param  target: 需要位或值的地址。
* @param[in]  value: 需要位或的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32_or(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_or)
1:
	ldrex  r2, [r0]	        /* 加载旧值到r2,并标记 独占访问 */
	orr	   r3, r2, r1	    /* 或上 value，结果存到r3 */
	strex  r12, r3, [r0]	/* 尝试存储r3 */
	teq	   r12, #0		    /* 判断是否存储成功 */
	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, r2		    /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_or)
     
/*
* @brief 
*       将target指向的值与value位与，并返回target原有的值。
* @param  target: 需要位与值的地址。
* @param[in]  value: 需要位与的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32_and(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_and)
1:
	ldrex  r2, [r0]	         /* 加载旧值到r2,并标记 独占访问 */
	and	   r3, r2, r1	     /* 与上 value，结果存到r3 */
	strex  r12, r3, [r0]	 /* 尝试存储r3 */
	teq	   r12, #0		     /* 判断是否存储成功 */
	bne	   1b	             /* 未存储成功，则跳转重试 */

	mov	   r0, r2		     /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_and)

/*
* @brief 
*       设置target指向的值为value，并返回target原有的值。
* @param  target: 需要设置值的地址。
* @param[in]  value: 需要设置的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32_set(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_set)
1:
	ldrex  r2, [r0]	        /* 加载旧值到r2,并标记 独占访问 */
	strex  r12, r1, [r0]	/* 尝试存储value */
	teq	   r12, #0		    /* 判断是否存储成功 */

	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, r2		    /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_set)

/*
* @brief 
*       获取target指向的值。
* @param  target: 需要获取值的地址。
* @return target指向的值。
*/
/*
* @brief 
*       获取target指向的值。
* @param  target: 需要获取值的地址。
* @return 获取target指向的值
*/
/* T_UWORD atomic32_get(T_UWORD * target) */
ENTRY(atomic32_get)
	ldr	   r0, [r0]        
	mov	   pc, lr
ENDPROC(atomic32_get)

/*
* @brief
*       将target指向的值增加1，并返回target原有的值。
* @param  target: 需要增加值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32_inc(T_UWORD * target) */
ENTRY(atomic32_inc)
1:
	ldrex  r2, [r0]	        /* 加载旧值到r2,并标记 独占访问 */
	add	   r3, r2, #1	    /* 旧值 加1 */
	strex  r12, r3, [r0]	/* 尝试存储相加后的结果 */
	teq	   r12, #0		    /* 判断是否存储成功 */

	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, r2		    /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_inc)

/*
* @brief
*		将target指向的值增加1，并返回target增加后的值。
* @param  target: 需要增加值的地址。
* @return target增加后的值
*/
/* T_UWORD atomic32_inc_return(T_UWORD * target) */
ENTRY(atomic32_inc_return)
1:
	ldrex  r2, [r0] 		/* 加载旧值到r2,并标记 独占访问 */
	add    r3, r2, #1		/* 旧值 加1 */
	strex  r12, r3, [r0]	/* 尝试存储相加后的结果 */
	teq    r12, #0			/* 判断是否存储成功 */

	bne    1b				/* 未存储成功，则跳转重试 */

	mov    r0, r3			/* 返回新值 */
	mov    pc, lr
ENDPROC(atomic32_inc_return)

/*
* @brief
*       将target指向的值减去1，并返回target原有的值。
* @param  target: 需要减去值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32_dec(T_UWORD * target) */
ENTRY(atomic32_dec)
1:
	ldrex  r2, [r0]	        /* 加载旧值到r2,并标记 独占访问 */
	sub	   r3, r2, #1	    /* 旧值 减1 */
	strex  r12, r3, [r0]	/* 尝试存储相减后的结果 */
	teq	   r12, #0		    /* 判断是否存储成功 */

	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, r2		    /* 返回旧值 */
	mov	   pc, lr
ENDPROC(atomic32_dec)

/*
* @brief
*		将target指向的值减去1，并返回target减去后的值。
* @param  target: 需要减去值的地址。
* @return target减去后的值
*/
/* T_UWORD atomic32_dec_return(T_UWORD * target) */
ENTRY(atomic32_dec_return)
1:
	ldrex  r2, [r0] 		/* 加载旧值到r2,并标记 独占访问 */
	sub    r3, r2, #1		/* 旧值 减1 */
	strex  r12, r3, [r0]	/* 尝试存储相减后的结果 */
	teq    r12, #0			/* 判断是否存储成功 */

	bne    1b				/* 未存储成功，则跳转重试 */

	mov    r0, r3			/* 返回新值 */
	mov    pc, lr
ENDPROC(atomic32_dec_return)

/*
* @brief 
*       将target指向的值与oldValue对比，如果相等，设置target指向的值为newValue，
*       并返回TRUE；如果不相等，则直接返回FALSE。
* @@param  target: 需要对比值的地址。
* @param[in]  oldValue: 需要对比的值。
* @param[in]  newValue: 需要设置的值。
* @return TURE:target指向的值与oldValue相等
* @return FALSE:target指向的值与oldValue不相等
*/
/* BOOL atomic32_cas(T_UWORD * target,T_UWORD oldValue,T_UWORD newValue) */
ENTRY(atomic32_cas)
1:
	ldrex  r3, [r0]	        /* 加载target值到r3,并标记 独占访问 */
	teq    r3, r1	        /* 比较*target和oldValue的值是否相等 */
	bne    2f               /* 不等，则跳到标记2 */
	strex  r12, r2, [r0]	/* 尝试存储newValue */
	teq	   r12, #0		    /* 判断是否存储成功 */

	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, #1		    /* 存储成功,设置返回值1 */
	mov	   pc, lr
2:
	CLREX                   
	mov	   r0, #0		    /* *target和oldValue的值不等 返回0 */
	mov	   pc, lr
ENDPROC(atomic32_cas)

/*
* @brief 
*       将target指向的值减去value，并返回target原有的值。
* @param  target: 需要减去值的地址。
* @param[in]  value: 需要减去的值。
* @return target原有指向的值
*/
/* T_UWORD atomic32_sub(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_sub)
1:
	ldrex  r2, [r0]	        /* 加载target值到r2,并标记 独占访问 */
	SUB    r3, r2, r1	    /* *target - value */
	strex  r12, r3, [r0]	/* 尝试存储相减的结果 */
	teq	   r12, #0		    /*  判断是否存储成功 */

	bne	   1b	            /* 未存储成功，则跳转重试 */

	mov	   r0, r2		    /* target原有指向的值 */
	mov	   pc, lr
ENDPROC(atomic32_sub)

/*
* @brief 
*		将target指向的值减去value，并返回target减去后的值。
* @param  target: 需要减去值的地址。
* @param[in]  value: 需要减去的值。
* @return target减去后的值
*/
/* T_UWORD atomic32_sub_return(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_sub_return)
1:
	ldrex  r2, [r0] 		/* 加载target值到r2,并标记 独占访问 */
	SUB    r3, r2, r1		/* *target - value */
	strex  r12, r3, [r0]	/* 尝试存储相减的结果 */
	teq    r12, #0			/*	判断是否存储成功 */

	bne    1b				/* 未存储成功，则跳转重试 */

	mov    r0, r3			/* 返回新值 */
	mov    pc, lr
ENDPROC(atomic32_sub_return)

/*
* @brief 
*       清除target指向的值，并返回target原有的值。
* @param  target: 需要清除值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32_clear(T_UWORD * target) */
ENTRY(atomic32_clear)
	mov    r1, #0
1:
	ldrex  r2, [r0]	      /* 加载*target值到r2,并标记 独占访问 */
	strex  r12, r1, [r0]  /* 尝试*target=0 */
	teq	   r12, #0		  /* 判断是否存储成功 */

	bne	   1b	          /* 未存储成功，则跳转重试 */

	mov	   r0, r2		  /* target原有指向的值 */
	mov	   pc, lr
ENDPROC(atomic32_clear)

/*
* @brief 
*       将target指向的值与value位异或，并返回target原有的值。
* @param  target: 需要位异或值的地址。
* @param[in]  value: 需要位异或的值。
* @return target原有指向的值
*/
/* T_UWORD atomic32_xor(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_xor)
1:
	ldrex  r2, [r0]	            /* 加载旧值到r2,并标记 独占访问 */
	eor	   r3, r2, r1	        /* *target异或value */
	strex  r12, r3, [r0]	    /* 尝试存储异或的结果 */
	teq	   r12, #0		        /* 判断是否存储成功 */

	bne	   1b	                /* 未存储成功，则跳转重试 */

	mov	   r0, r2		        /* target原有指向的值 */
	mov	   pc, lr
ENDPROC(atomic32_xor)
