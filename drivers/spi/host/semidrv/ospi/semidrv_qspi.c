#include <assert.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <errno.h>
#include <fs/fs.h>
#include <fs/ioctl.h>
#include <io.h>
#include <kmalloc.h>
#include <linux/macros.h>
#include <linux/types.h>
#include <ttos.h>
#include <ttosMM.h>
#include <ttos_init.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include <mtd/mtd.h>
#include "spi_nor.h"

#include <sys/types.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>

#include <driver/of.h>
#include <driver/driver.h>
#include <driver/devbus.h>
#include <driver/spi/spi_transfer.h>
#include <ttosMM.h>
#include <ttos_init.h>
#include <io.h>
#include "semidrv_qspi.h"
#include <stdarg.h>


#include "cdns_ospi_drv.h"
#include "cdns_ospi.h"

#include "debug.h"
#undef KLOG_TAG
#define KLOG_TAG    "sdrv-qspi-flash"
#include <klog.h>
#include "spi_nor.h"
#include <uaccess.h>

#ifndef OSPI_IDX
#ifdef OSPI2_BOOT
#define OSPI_IDX 2
#else
#define OSPI_IDX 1
#endif
#endif

#define TMP_DATA_NUM	8192
#define SEC_DATA_NUM	4096

int printk (const char *fmt, ...);

void board_norflash_init(sdrv_qspi_flash_t *spi);

void ospi_lock(MUTEX_ID mtxId)
{
	TTOS_ObtainMutex(mtxId, TTOS_MUTEX_WAIT_FOREVER);//TTOS_MUTEX_WAIT_FOREVER
}

void ospi_unlock(MUTEX_ID mtxId)
{
	TTOS_ReleaseMutex (mtxId);
}

static int sdrv_ospi_norflash_open(struct inode *inode)
{
    int ret = 0;
	assert (inode->i_private);
    return ret;
}

static int sdrv_ospi_norflash_close (struct inode *inode)
{
    int ret = 0;
	assert (inode->i_private);

    return ret;
}

static ssize_t sdrv_ospi_norflash_read (struct inode *inode, unsigned char *buffer,
                           blkcnt_t startsector, unsigned int nsectors)
{
    int ret = 0;
    int result = 0;
    sdrv_qspi_flash_t *flash = NULL;
    uint32_t blocksize;

    if (inode && inode->i_private)
    {
        flash = (sdrv_qspi_flash_t *)inode->i_private;
        if (flash)
        {
            blocksize = flash->erase_gran;

			ospi_lock(flash->lock);
		    
			result = ospi_norflash_read_data(&(flash->ctrl.nor), startsector * blocksize, buffer, nsectors * blocksize);

		    ospi_unlock(flash->lock);            

            if (0 == result)
            {
                ret = nsectors * blocksize;
            }
            else
            {
            	printk("%s:%d\n", __FUNCTION__, result);
            }
        }
    }

    return ret;
}

static ssize_t sdrv_ospi_norflash_write (struct inode *inode,const unsigned char *buffer,
                            blkcnt_t startsector, unsigned int nsectors)
{
    int ret = -1;
    int result = 0;
    sdrv_qspi_flash_t *flash = NULL;
    uint32_t blocksize;

    if (inode && inode->i_private)
    {
        flash = (sdrv_qspi_flash_t *)inode->i_private;
        if (flash)
        {
            blocksize = flash->erase_gran;
            ospi_lock(flash->lock);
			result = ospi_norflash_write_data(&(flash->ctrl.nor), startsector * blocksize, (uint8_t*)buffer, nsectors * blocksize);
            ospi_unlock(flash->lock);
            ret = -1;
            if (0 == result)
            {
                ret = nsectors * blocksize;
            }
            else
            {
            	printk("%s:%d\n", __FUNCTION__, result);
            }
        }
    }

    return ret;
}

static int sdrv_ospi_norflash_geometry (struct inode *inode, struct geometry *geometry)
{
    int ret;
    sdrv_qspi_flash_t *flash = NULL;

    if (inode && inode->i_private)
    {
        flash = (sdrv_qspi_flash_t *)inode->i_private;
        if (flash && geometry)
        {
            geometry->geo_available    = true;
            geometry->geo_mediachanged = false;
            geometry->geo_writeenabled = true;
            geometry->geo_nsectors     = flash->capacity / flash->erase_gran;
            geometry->geo_sectorsize   = flash->erase_gran;
        }
    }

    return 0;
}

int sdrv_ospi_norflash_ioctl(struct inode *inode, int cmd, unsigned long arg)
{
    int ret = 0;
    int result = 0;
    int i = 0;
    char id[4] = {0xff,0xff,0xff};
	sdrv_qspi_flash_t *flash = NULL;
	unsigned long *array = NULL;
	unsigned long sAddr = 0;
	unsigned long sOff = 0;
	size_t len = 0;	
	uint8_t sts = 0;
	uint8_t *u_sts = NULL;
	flash = (sdrv_qspi_flash_t *)inode->i_private;
	/*  */
	if (inode && inode->i_private)
    {
        flash = (sdrv_qspi_flash_t *)inode->i_private;
    }
    if(flash == NULL)
    {
    	return ENOENT;
    }
	ospi_lock(flash->lock);
    switch (cmd)
    {
    	case SPINOR_OP_BE_4K:	/* 4k擦除 */
    		if(arg != 0)
    		{
    			array = (unsigned long*)arg;
    			
				sAddr = ALIGN_DOWN(array[1], 4096);	/* 保证地址4K对齐 */
				len = ALIGN_DOWN(array[2], 4096);		/* 保证地址4K对齐 */
				if(len == 0)
				{
					len = 4096;
				}
				ret = ospi_norflash_erase(flash, SPINOR_OP_BE_4K, sAddr, len);
    		}
    		else
    		{
    			ret = EINVAL;
    		}
    		
    	break;
		case SPINOR_OP_BE_32K:
			//
		break;
		case SPINOR_OP_SE:
			if(arg != 0)
    		{
    			array = (unsigned long*)arg;
    			sAddr = ALIGN_DOWN(array[1], 4096*16);	/* 保证地址4K对齐 */
				len = ALIGN_DOWN(array[2], 4096*16);		/* 保证地址4K对齐 */
				if(len == 0)
				{
					len = 4096*16;
				}
				ret = ospi_norflash_erase(flash, SPINOR_OP_SE, sAddr, len);
    		}
    		else
    		{
    			ret = EINVAL;
    		}
		break;
		case SPINOR_OP_CHIP_ERASE:
			//ospi_norflash_erase(flash, SPINOR_OP_CHIP_ERASE, 0, 0);
		break;    	
    	case SPINOR_OP_RDID:
    	case SPINOR_OP_RDSFDP:
			ret = ospi_norflash_read_info(&(flash->ctrl.nor), cmd, (char*)id);
			copy_to_user((void*)arg, id, 4);
    	break;
		case SPINOR_OP_READ:
			/* 读: arg{buffer,addr,len}*/
			array = (unsigned long*)arg;

			if(array[2] > SEC_DATA_NUM)
			{
				ret = -1;
				break;	/* 一次读取的数据不能大于4K */
			}
			memset(flash->tmpdata, 0, TMP_DATA_NUM);

			/* 按4k读写 */
			sAddr = ALIGN_DOWN(array[1], SEC_DATA_NUM);
			sOff = array[1] - sAddr;
			len = ALIGN_UP(sOff + array[2], SEC_DATA_NUM);
			result = ospi_norflash_read_data(&(flash->ctrl.nor), sAddr, flash->tmpdata, len);
			if(result == 0)
			{
				ret = array[2];	/* 设置返回值 */
				copy_to_user((void*)array[0], flash->tmpdata + sOff, array[2]);
			}
			else
			{
				ret = -1;
			}
		break;
		case SPINOR_OP_PP:
			/* 写: arg{buffer,addr,len}*/
			array = (unsigned long*)arg;
			
			sAddr = ALIGN_DOWN(array[1], SEC_DATA_NUM);
	 		sOff = array[1] - sAddr;
			if(array[2] > SEC_DATA_NUM)
			{
				ret = -1;
				break;	/* 一次写入的数据不能大于4K */
			}
			if(sOff != 0)
			{
				/* 需要先读数据 */
				result = ospi_norflash_read_data(&(flash->ctrl.nor), sAddr, flash->tmpdata, SEC_DATA_NUM);
				memset(flash->tmpdata + sOff, 0xFF, (TMP_DATA_NUM - sOff));
			}
			copy_from_user(flash->tmpdata + sOff, (void*)array[0], (unsigned int)array[2]);	
			len = ALIGN_UP(sOff + array[2], SEC_DATA_NUM);/* 必须对齐,不然写失败 */
			result = ospi_norflash_write_data(&(flash->ctrl.nor), sAddr,
								(uint8_t*)(flash->tmpdata),len /* sOff + array[2] */ );
			if(result == 0)
			{
				ret = array[2];	/* 设置写入成功的数量 */
			}
			else
			{
				ret = -1;
			}
		break;

		case SPINOR_OP_RDSR:	/* 获取flash状态 */
		sts = 0;
		u_sts = (uint8_t*)arg;
		ret = ospi_norflash_status(&(flash->ctrl.nor), &sts);
		if(ret == 0)
		{
			copy_to_user((void*)u_sts, &sts, 1);
		}

		break;
        default:
        ret = -ENOTTY;
        break;
    }
	ospi_unlock(flash->lock);

    return ret;
}



static const struct block_operations sdrv_bops = {
    sdrv_ospi_norflash_open,     /* open     */
    sdrv_ospi_norflash_close,    /* close    */
    sdrv_ospi_norflash_read,     /* read     */
    sdrv_ospi_norflash_write,    /* write    */
    sdrv_ospi_norflash_geometry, /* geometry */
    sdrv_ospi_norflash_ioctl     /* ioctl    */
};

static int sdrv_nor_dev_register(const sdrv_qspi_flash_t *flash)
{
    int result = -1;
    int ret = 0;
    char devname[32] = {0};
    if (flash->name)
    {
        snprintf (devname, 32, "/dev/%s", flash->name);
        printk("[%s]\n", devname);
        ret = register_blockdriver (devname, &sdrv_bops, 0666, (void *)flash);
        if (ret >= 0)
        {
            result = 0;
        }
    }

    return result;
}


static int sdrv_qspi_probe(struct device *dev)
{
    sdrv_qspi_flash_t *ospi_nor = NULL;

    int reg_base[8] = {0};
	int ret  = 0;
    
	if (dev->of_node && dev->of_node->child)
    {
        struct device_node *child = dev->of_node->child;
        for (; child; child = child->sibling)
        {
        	of_property_read_u32_array(child, "reg", reg_base, 2);
			uintptr_t cs  = reg_base[0];
			uintptr_t fl_size  = reg_base[1];
			ospi_nor = calloc(1, sizeof(sdrv_qspi_flash_t));
			if (NULL == ospi_nor)
			{
				KLOG_E ("No enough memory");
				return -ENOMEM;
			}
	   	    of_property_read_u32_array(dev->of_node, "reg", reg_base, 8);
		    ospi_nor->apb  = (uintptr_t)ioremap (reg_base[1], reg_base[3]);
			ospi_nor->ahb  = (uintptr_t)ioremap (reg_base[5], reg_base[7]);
			if(ospi_nor->apb == 0 || ospi_nor->ahb == 0)
			{
				free(ospi_nor);
				return -1;
			}
			ospi_nor->cs = cs;
			ospi_nor->fl_size = fl_size;
			
			TTOS_CreateMutex (1, 0, &ospi_nor->lock);
			ospi_nor->dev = dev;
			ospi_nor->tmpdata = calloc(1, TMP_DATA_NUM);	/* 用与数据的临时保存 */
			board_norflash_init(ospi_nor);
			strcpy(ospi_nor->name,child->name);
			/* 注册文件系统 */
			sdrv_nor_dev_register(ospi_nor);
		}
	}

    return 0;
}


static struct of_device_id sdr_qspi_table[] = {
    {.compatible = "sdrv,ospi-nor",},
    { /* end of list */ },
};

static struct driver sdr_qspi_driver = {
    .name        = "qspi-flash",
    .probe       = sdrv_qspi_probe,
    .match_table = sdr_qspi_table,
};

static int __sdrv_qspi_init(void)
{
    return platform_add_driver(&sdr_qspi_driver);
}
INIT_EXPORT_DRIVER(__sdrv_qspi_init, "semidrv ospi flash init");
